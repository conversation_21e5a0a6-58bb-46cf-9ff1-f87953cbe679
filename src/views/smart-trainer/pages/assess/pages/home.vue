<template>
    <!-- 考试列表主界面 -->
    <div class="exam-list">
        <!-- 顶部搜索框 -->
        <div class="search-bar">
            <div class="search-input-wrapper">
                <i class="pi pi-search search-icon"></i>
                <input
                    type="text"
                    class="search-input"
                    placeholder="搜索考试名称、类型或标签..."
                    v-model="searchKeyword"
                />
                <i v-if="searchKeyword" class="pi pi-times clear-icon" @click="clearSearch"></i>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <!-- 部门筛选 -->
            <div class="filter-group">
                <div class="filter-options">
                    <button
                        class="filter-btn"
                        :class="{ active: !selectedDepartment }"
                        @click="handleDepartmentChange(null)"
                    >
                        全部
                    </button>
                    <button
                        v-for="department in departments"
                        :key="department.id"
                        class="filter-btn"
                        :class="{ active: selectedDepartment?.id === department.id }"
                        @click="handleDepartmentChange(department)"
                    >
                        {{ department.name }}
                    </button>
                </div>
            </div>

            <!-- 考试类型筛选 -->
            <div class="filter-group">
                <div class="filter-options">
                    <button
                        class="filter-btn"
                        :class="{ active: !selectedType }"
                        @click="handleTypeChange(null)"
                    >
                        全部
                    </button>
                    <button
                        v-for="type in examTypes"
                        :key="type.id"
                        class="filter-btn"
                        :class="{ active: selectedType?.id === type.id }"
                        @click="handleTypeChange(type)"
                    >
                        {{ type.name }}
                    </button>
                </div>
            </div>
        </div>

        <!-- 考试列表 -->
        <div class="exam-list-container">
            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载考试列表...</p>
            </div>

            <!-- 考试列表内容 -->
            <template v-else>
                <div
                    v-for="exam in filteredExams"
                    :key="exam.id"
                    class="exam-item"
                    @click="handleExamClick(exam)"
                >
                    <!-- 左侧考试图标 -->
                    <div class="exam-icon">
                        <div class="icon-container" :class="exam.type">
                            <i :class="`pi ${exam.icon}`" class="exam-icon-img"></i>
                        </div>
                    </div>

                    <!-- 右侧考试内容 -->
                    <div class="exam-content">
                        <!-- 考试标题 -->
                        <div class="exam-header">
                            <h3 class="exam-title">{{ exam.name }}</h3>
                        </div>

                        <!-- 标签区域 -->
                        <div class="exam-tags">
                            <span v-for="tag in exam.tags" :key="tag" class="exam-tag">
                                {{ tag }}
                            </span>
                        </div>

                        <!-- 考试统计信息 -->
                        <div class="exam-stats">
                            <div class="stat-item">
                                <i class="pi pi-file"></i>
                                <span>{{ exam.questionCount }}题</span>
                            </div>
                            <div class="stat-item">
                                <i class="pi pi-star"></i>
                                <span>{{ exam.difficulty }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧箭头 -->
                    <div class="exam-actions">
                        <i class="pi pi-chevron-right action-arrow"></i>
                    </div>
                </div>

                <!-- 空状态 -->
                <div v-if="filteredExams.length === 0" class="empty-state">
                    <i class="pi pi-inbox empty-icon"></i>
                    <p class="empty-text">暂无符合条件的考试</p>
                    <p class="empty-hint">请尝试调整筛选条件</p>
                </div>
            </template>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';

// 路由
const router = useRouter();

// 响应式数据
const selectedDepartment = ref(null);
const selectedType = ref(null);
const loading = ref(false);
const searchKeyword = ref('');

// Mock数据 - 部门列表
const departments = ref([
    { id: 1, name: '之家-通用', code: 'zhijia-general' },
    { id: 2, name: '之家-网点', code: 'zhijia-branch' },
    { id: 3, name: '之家-大客', code: 'zhijia-enterprise' }
]);

// Mock数据 - 考试类型
const examTypes = ref([
    { id: 1, name: '廉洁合规' },
    { id: 2, name: '业务技能' },
    { id: 3, name: '安全培训' },
    { id: 4, name: '产品知识' }
]);

// Mock数据 - 考试列表
const exams = ref([
    {
        id: 'exam_001',
        name: '廉洁合规测试',
        description: '本测试包含单选题、多选题和判断题，请仔细阅读题目后作答',
        type: 'compliance',
        typeLabel: '廉洁合规',
        departmentId: 1,
        typeId: 1,
        duration: 30,
        questionCount: 15,
        difficulty: '中等',
        status: 'available',
        icon: 'pi-shield',
        tags: ['廉洁', '合规', '必修']
    },
    {
        id: 'exam_002',
        name: '汽车销售技能考核',
        description: '测试汽车销售相关的专业知识和技能掌握情况',
        type: 'skill',
        typeLabel: '业务技能',
        departmentId: 2,
        typeId: 2,
        duration: 45,
        questionCount: 20,
        difficulty: '困难',
        status: 'available',
        icon: 'pi-car',
        tags: ['销售', '技能', '专业']
    },
    {
        id: 'exam_003',
        name: '信息安全培训考试',
        description: '涵盖网络安全、数据保护、密码管理等安全知识',
        type: 'security',
        typeLabel: '安全培训',
        departmentId: 1,
        typeId: 3,
        duration: 25,
        questionCount: 12,
        difficulty: '简单',
        status: 'available',
        icon: 'pi-lock',
        tags: ['安全', '培训', '必修']
    },
    {
        id: 'exam_004',
        name: '新车型产品知识测试',
        description: '最新上市车型的产品特点、配置参数等知识考核',
        type: 'product',
        typeLabel: '产品知识',
        departmentId: 2,
        typeId: 4,
        duration: 35,
        questionCount: 18,
        difficulty: '中等',
        status: 'available',
        icon: 'pi-cog',
        tags: ['产品', '新车', '知识']
    },
    {
        id: 'exam_005',
        name: '客户服务标准考试',
        description: '客户接待、服务流程、投诉处理等服务标准考核',
        type: 'service',
        typeLabel: '业务技能',
        departmentId: 3,
        typeId: 2,
        duration: 40,
        questionCount: 16,
        difficulty: '中等',
        status: 'completed',
        icon: 'pi-users',
        tags: ['服务', '客户', '标准']
    },
    {
        id: 'exam_006',
        name: '客户服务标准考试',
        description: '客户接待、服务流程、投诉处理等服务标准考核',
        type: 'service',
        typeLabel: '业务技能',
        departmentId: 3,
        typeId: 2,
        duration: 40,
        questionCount: 16,
        difficulty: '中等',
        status: 'completed',
        icon: 'pi-users',
        tags: ['服务', '客户', '标准']
    },
    {
        id: 'exam_007',
        name: '客户服务标准考试',
        description: '客户接待、服务流程、投诉处理等服务标准考核',
        type: 'service',
        typeLabel: '业务技能',
        departmentId: 3,
        typeId: 2,
        duration: 40,
        questionCount: 16,
        difficulty: '中等',
        status: 'completed',
        icon: 'pi-users',
        tags: ['服务', '客户', '标准']
    }
]);

// 计算属性 - 根据筛选条件过滤考试
const filteredExams = computed(() => {
    let result = exams.value;

    // 按部门筛选
    if (selectedDepartment.value) {
        result = result.filter(exam => exam.departmentId === selectedDepartment.value.id);
    }

    // 按类型筛选
    if (selectedType.value) {
        result = result.filter(exam => exam.typeId === selectedType.value.id);
    }

    // 按关键词搜索
    if (searchKeyword.value) {
        result = result.filter(
            exam =>
                exam.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
                exam.description.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
                exam.typeLabel.toLowerCase().includes(searchKeyword.value.toLowerCase())
        );
    }

    return result;
});

onMounted(() => {
    console.log('考试列表页已加载');
});

// 方法
const clearSearch = () => {
    searchKeyword.value = '';
};

const handleDepartmentChange = department => {
    loading.value = true;
    selectedDepartment.value = department;

    // 模拟加载延迟
    setTimeout(() => {
        loading.value = false;
    }, 300);
};

const handleTypeChange = type => {
    loading.value = true;
    selectedType.value = type;

    // 模拟加载延迟
    setTimeout(() => {
        loading.value = false;
    }, 300);
};

const handleExamClick = exam => {
    console.log('点击考试:', exam);
    // 跳转到考试详情页
    router.push(`/smart-trainer/assess/detail/${exam.id}`);
};
</script>

<style lang="scss" scoped>
.exam-list {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

// 搜索栏样式 - 参考CourseFilter
.search-bar {
    background: white;
    padding: 12px 16px;
    flex-shrink: 0;

    .search-input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        background: #f8f9fa;
        border-radius: 20px;
        padding: 8px 16px;

        .search-icon {
            color: #999;
            margin-right: 8px;
            font-size: 16px;
        }

        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            outline: none;
            font-size: 14px;
            color: #333;

            &::placeholder {
                color: #999;
            }
        }

        .clear-icon {
            color: #999;
            cursor: pointer;
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            transition: color 0.2s ease;

            &:hover {
                color: #666;
            }
        }
    }
}

// 筛选区域样式 - 参考CourseFilter的布局风格
.filter-section {
    background: white;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    flex-shrink: 0;

    .filter-group {
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 0;
        }

        .filter-label {
            display: block;
            font-size: 15px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 12px;
        }

        .filter-options {
            display: flex;
            flex-wrap: nowrap;
            gap: 10px;
            overflow-x: auto;
            padding-bottom: 4px;

            // 隐藏滚动条但保持滚动功能
            scrollbar-width: none;
            -ms-overflow-style: none;
            &::-webkit-scrollbar {
                display: none;
            }

            .filter-btn {
                padding: 8px 16px;
                border: none;
                border-radius: 18px;
                background: #f5f5f5;
                color: #666;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                white-space: nowrap;
                flex-shrink: 0;

                &:hover {
                    background: #e6f7ff;
                    color: #1677ff;
                }

                &.active {
                    background: #1677ff;
                    color: white;
                    font-weight: 600;
                }
            }
        }
    }
}

// 考试列表样式
.exam-list-container {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    &::-webkit-scrollbar {
        display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;
    padding: 16px;

    // 隐藏滚动条但保持滚动功能
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
        display: none;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #8c8c8c;
        background: white;
        border-radius: 16px;
        margin: 20px 0;

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f0f0f0;
            border-top: 3px solid #1677ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        p {
            font-size: 15px;
            color: #8c8c8c;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }
    }

    .exam-item {
        background: white;
        border-radius: 16px;
        padding: 16px;
        margin-bottom: 12px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid rgba(0, 0, 0, 0.04);
        display: flex;
        align-items: center;
        gap: 14px;

        &:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
            border-color: rgba(22, 119, 255, 0.1);
            transform: translateY(-2px);
        }

        &:last-child {
            margin-bottom: 0;
        }

        .exam-icon {
            flex-shrink: 0;

            .icon-container {
                width: 50px;
                height: 50px;
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;

                &.compliance {
                    background: #6366f1;
                }

                &.skill {
                    background: #8b5cf6;
                }

                &.security {
                    background: #06b6d4;
                }

                &.product {
                    background: #10b981;
                }

                &.service {
                    background: #f59e0b;
                }

                .exam-icon-img {
                    font-size: 22px;
                    color: white;
                }
            }
        }

        .exam-content {
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .exam-header {
            .exam-title {
                font-size: 16px;
                font-weight: 600;
                color: #262626;
                margin: 0;
                line-height: 1.4;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                line-clamp: 1;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }
        }

        .exam-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 2px;

            .exam-tag {
                background: #f1f5f9;
                color: #475569;
                font-size: 12px;
                font-weight: 500;
                padding: 2px 8px;
                border-radius: 10px;
                white-space: nowrap;
                border: 1px solid #e2e8f0;

                &:nth-child(2n) {
                    background: #eff6ff;
                    color: #1e40af;
                    border-color: #bfdbfe;
                }

                &:nth-child(3n) {
                    background: #f0fdf4;
                    color: #166534;
                    border-color: #bbf7d0;
                }

                &:nth-child(4n) {
                    background: #fefce8;
                    color: #a16207;
                    border-color: #fde047;
                }

                &:nth-child(5n) {
                    background: #faf5ff;
                    color: #7c2d12;
                    border-color: #d8b4fe;
                }
            }
        }

        .exam-stats {
            display: flex;
            gap: 16px;
            align-items: center;

            .stat-item {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 13px;
                color: #595959;

                i {
                    font-size: 12px;
                    color: #8c8c8c;
                    flex-shrink: 0;
                }

                span {
                    font-weight: 500;
                }
            }
        }

        .exam-actions {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            margin-left: 8px;

            .action-arrow {
                font-size: 16px;
                color: #d9d9d9;
                transition: color 0.2s ease;
            }
        }

        &:hover .exam-actions .action-arrow {
            color: #1677ff;
        }
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #8c8c8c;
        background: white;
        border-radius: 16px;
        margin: 20px 0;

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
            color: #d9d9d9;
        }

        .empty-text {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 4px;
            color: #595959;
        }

        .empty-hint {
            font-size: 14px;
            opacity: 0.7;
            color: #8c8c8c;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .exam-list-container {
        padding: 12px;

        .exam-item {
            padding: 16px;
            gap: 12px;

            .exam-icon .icon-container {
                width: 48px;
                height: 48px;

                .exam-icon-img {
                    font-size: 20px;
                }
            }

            .exam-content {
                gap: 6px;

                .exam-header .exam-title {
                    font-size: 15px;
                    line-height: 1.3;
                }

                .exam-tags .exam-tag {
                    font-size: 11px;
                    padding: 2px 6px;
                }

                .exam-stats {
                    gap: 12px;

                    .stat-item {
                        font-size: 12px;

                        i {
                            font-size: 11px;
                        }
                    }
                }
            }

            .exam-actions .action-arrow {
                font-size: 14px;
            }
        }
    }

    .filter-section {
        padding: 12px;

        .filter-group {
            margin-bottom: 12px;

            .filter-label {
                font-size: 14px;
                margin-bottom: 10px;
            }

            .filter-options .filter-btn {
                padding: 6px 12px;
                font-size: 13px;
            }
        }
    }

    .search-bar {
        padding: 10px 12px;

        .search-input-wrapper {
            padding: 8px 16px;

            .search-icon {
                font-size: 16px;
            }

            .search-input {
                font-size: 13px;
            }

            .clear-icon {
                font-size: 16px;
            }
        }
    }
}

@media (max-width: 480px) {
    .exam-list-container {
        padding: 10px;

        .exam-item {
            padding: 12px;
            gap: 10px;
            border-radius: 12px;

            .exam-icon .icon-container {
                width: 40px;
                height: 40px;
                border-radius: 10px;

                .exam-icon-img {
                    font-size: 18px;
                }
            }

            .exam-content {
                gap: 4px;

                .exam-header .exam-title {
                    font-size: 14px;
                    line-height: 1.2;
                }

                .exam-tags .exam-tag {
                    font-size: 10px;
                    padding: 1px 5px;
                    border-radius: 8px;
                }

                .exam-stats {
                    gap: 10px;

                    .stat-item {
                        font-size: 12px;
                        gap: 3px;

                        i {
                            font-size: 10px;
                        }
                    }
                }
            }

            .exam-actions {
                margin-left: 4px;

                .action-arrow {
                    font-size: 14px;
                }
            }
        }
    }

    .filter-section .filter-group .filter-options {
        gap: 6px;

        .filter-btn {
            padding: 6px 10px;
            font-size: 12px;
        }
    }
}
</style>
